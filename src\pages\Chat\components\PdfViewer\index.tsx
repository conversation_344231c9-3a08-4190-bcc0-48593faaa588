import closeIcon from '@/assets/chatpdf/btn_close.svg';
import thumbControlIcon from '@/assets/chatpdf/btn_thumbcontrol.svg';
import toBigIcon from '@/assets/chatpdf/btn_tobig.svg';
import { dispatchInUtils } from '@/utils';
import classNames from 'classnames';
import { FC, useEffect, useRef, useState } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import 'react-pdf/dist/Page/AnnotationLayer.css';
import 'react-pdf/dist/Page/TextLayer.css';
import styled from './index.less';

pdfjs.GlobalWorkerOptions.workerSrc = `/pdfjs-dist/build/pdf.worker.min.mjs`;

interface Props {
  name: string;
  url: string;
  isRoute?: boolean;
}

const PdfViewer: FC<Props> = ({ name, url, isRoute = false }) => {
  const [numPages, setNumPages] = useState<number>();
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [scale, setScale] = useState<number>(0.7);
  const [showSidebar, setShowSidebar] = useState<boolean>(false);
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);
  const mainContentRef = useRef<HTMLDivElement>(null);
  const sidebarRef = useRef<HTMLDivElement>(null);
  const isInitialScroll = useRef(true);
  const isUserNavigating = useRef(false);

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
  };

  const handleToggleButton = () => {
    const newShowSidebar = !showSidebar;
    setShowSidebar(newShowSidebar);

    // 当侧边栏打开时，滚动到当前页面
    if (newShowSidebar) {
      setTimeout(() => {
        if (sidebarRef.current) {
          const thumbnailElement = document.querySelector(
            `.${styled.thumbnail}[data-page="${pageNumber}"]`,
          );
          if (thumbnailElement) {
            thumbnailElement.scrollIntoView({
              behavior: 'instant', // 使用instant避免动画闪烁
              block: 'center',
            });
          }
        }
      }, 100);
    }
  };

  const handlePresentationMode = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  const goToPage = (page: number) => {
    isUserNavigating.current = true;
    setPageNumber(page);

    setTimeout(() => {
      const element = document.getElementById(`page_${page}`);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }

      // 确保侧边栏也同步滚动到对应位置
      if (showSidebar) {
        setTimeout(() => {
          if (sidebarRef.current) {
            const thumbnailElement = document.querySelector(
              `.${styled.thumbnail}[data-page="${page}"]`,
            );
            if (thumbnailElement) {
              thumbnailElement.scrollIntoView({
                behavior: 'instant', // 点击导航时使用instant避免闪烁
                block: 'center',
              });
            }
          }
          isUserNavigating.current = false;
        }, 0);
      } else {
        isUserNavigating.current = false;
      }
    }, 0);
  };

  // 监听主区域滚动，同步更新侧边栏
  useEffect(() => {
    const mainContent = mainContentRef.current;
    if (!mainContent) return;

    const handleScroll = () => {
      if (!numPages || !showSidebar || isUserNavigating.current) return;

      const scrollTop = mainContent.scrollTop;
      const containerHeight = mainContent.clientHeight;

      // 计算当前可视区域中心位置对应的页面
      const centerPosition = scrollTop + containerHeight / 2;

      let currentPage = 1;
      let accumulatedHeight = 0;

      // 遍历所有页面元素，找到中心位置对应的页面
      for (let i = 0; i < numPages; i++) {
        const pageElement = document.getElementById(`page_${i + 1}`);
        if (pageElement) {
          const pageHeight = pageElement.offsetHeight;
          const pageTop = accumulatedHeight;
          const pageBottom = accumulatedHeight + pageHeight;

          // 如果中心位置在当前页面范围内
          if (centerPosition >= pageTop && centerPosition <= pageBottom) {
            currentPage = i + 1;
            break;
          }

          accumulatedHeight += pageHeight + 20; // 20px是页面间距
        }
      }

      setPageNumber(currentPage);
    };

    mainContent.addEventListener('scroll', handleScroll);
    return () => {
      mainContent.removeEventListener('scroll', handleScroll);
    };
  }, [numPages, showSidebar]);

  // 当侧边栏打开且页面编号改变时，滚动到对应缩略图
  useEffect(() => {
    if (showSidebar && !isUserNavigating.current) {
      // 避免初始加载时的滚动
      if (isInitialScroll.current) {
        isInitialScroll.current = false;
        return;
      }

      setTimeout(() => {
        if (sidebarRef.current) {
          const thumbnailElement = document.querySelector(
            `.${styled.thumbnail}[data-page="${pageNumber}"]`,
          );
          if (thumbnailElement) {
            thumbnailElement.scrollIntoView({
              behavior: 'smooth', // 滚动同步时使用smooth动画
              block: 'center',
            });
          }
        }
      }, 100);
    }
  }, [pageNumber, showSidebar]);

  const handleClose = () => {
    dispatchInUtils({
      type: 'pageLayout/changePageMode',
      payload: '',
    });
    dispatchInUtils({
      type: 'pdfContainer/changeUrl',
      payload: {
        url: '',
        name: '',
        size: '',
      },
    });
  };

  return (
    <div className={styled.pdfViewer}>
      <div className={classNames(styled.pdfViewerToolsBar, { [styled.isRoute]: isRoute })}>
        <div className={styled.btnThumbControl} onClick={handleToggleButton}>
          <img src={thumbControlIcon} />
        </div>
        <div className={styled.title}>{name}</div>
        <div className={styled.btnToBig} onClick={handlePresentationMode}>
          <img src={toBigIcon} />
        </div>
        {!isRoute && (
          <div className={styled.btnClose} onClick={handleClose}>
            <img src={closeIcon} />
          </div>
        )}
      </div>
      <div className={styled.pdfContainer}>
        {/* 侧边栏缩略图 */}
        {showSidebar && numPages && (
          <div className={styled.sidebar} ref={sidebarRef}>
            <div className={styled.thumbnailContainer}>
              <Document file={url}>
                {Array.from(new Array(numPages), (_, index) => (
                  <div
                    key={`thumbnail_${index + 1}`}
                    className={`${styled.thumbnail} ${
                      pageNumber === index + 1 ? styled.activeThumbnail : ''
                    }`}
                    onClick={() => goToPage(index + 1)}
                    data-page={index + 1}
                  >
                    <Page pageNumber={index + 1} scale={0.09} className={styled.thumbnailPage} />
                    <div className={styled.pageNumber}>{index + 1}</div>
                  </div>
                ))}
              </Document>
            </div>
          </div>
        )}

        {/* 主要PDF显示区域 */}
        <div className={styled.mainContent} ref={mainContentRef}>
          <Document file={url} onLoadSuccess={onDocumentLoadSuccess} className={styled.pdfDocument}>
            {Array.from(new Array(numPages), (_, index) => (
              <div
                className={styled.pageContainer}
                key={`page_${index + 1}`}
                id={`page_${index + 1}`}
              >
                <Page pageNumber={index + 1} scale={scale} className={styled.pdfPage} />
              </div>
            ))}
          </Document>
        </div>
      </div>
    </div>
  );
};
export default PdfViewer;
