import closeIcon from '@/assets/chatpdf/btn_close.svg';
import thumbControlIcon from '@/assets/chatpdf/btn_thumbcontrol.svg';
import toBigIcon from '@/assets/chatpdf/btn_tobig.svg';
import { dispatchInUtils } from '@/utils';
import classNames from 'classnames';
import { FC, useEffect, useRef, useState } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import 'react-pdf/dist/Page/AnnotationLayer.css';
import 'react-pdf/dist/Page/TextLayer.css';
import styled from './index.less';

pdfjs.GlobalWorkerOptions.workerSrc = `/pdfjs-dist/build/pdf.worker.min.mjs`;

interface Props {
  name: string;
  url: string;
  isRoute?: boolean;
}

const PdfViewer: FC<Props> = ({ name, url, isRoute = false }) => {
  const [numPages, setNumPages] = useState<number>();
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [scale, setScale] = useState<number>(0.7);
  const [showSidebar, setShowSidebar] = useState<boolean>(false);
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);
  const mainContentRef = useRef<HTMLDivElement>(null);
  const sidebarRef = useRef<HTMLDivElement>(null);
  const isInitialScroll = useRef(true);
  const isUserNavigating = useRef(false);
  const isThumbnailClick = useRef(false); // 标记是否是点击缩略图导航

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
  };

  const handleToggleButton = () => {
    const newShowSidebar = !showSidebar;
    setShowSidebar(newShowSidebar);

    // 当侧边栏打开时，直接定位到当前页面对应的缩略图
    if (newShowSidebar) {
      // 设置标记，防止其他逻辑干扰
      isThumbnailClick.current = true;

      setTimeout(() => {
        if (sidebarRef.current) {
          const thumbnailElement = document.querySelector(
            `.${styled.thumbnail}[data-page="${pageNumber}"]`,
          );
          if (thumbnailElement) {
            thumbnailElement.scrollIntoView({
              behavior: 'instant', // 使用instant直接定位，不要滚动动画
              block: 'center',
            });
          }
        }

        // 重置标记
        setTimeout(() => {
          isThumbnailClick.current = false;
        }, 100);
      }, 150); // 稍微延长等待时间，确保侧边栏完全渲染
    }
  };

  const handlePresentationMode = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  const goToPage = (page: number) => {
    // 先设置标记，防止 useEffect 触发侧边栏滚动
    isThumbnailClick.current = true;
    isUserNavigating.current = true;

    setPageNumber(page);

    setTimeout(() => {
      const element = document.getElementById(`page_${page}`);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }

      // 点击侧边栏缩略图时，侧边栏不需要滚动，因为用户已经点击了目标位置
      isUserNavigating.current = false;

      // 延迟重置标记，确保所有相关的 useEffect 都已经执行完毕
      setTimeout(() => {
        isThumbnailClick.current = false;
      }, 500);
    }, 0);
  };

  // 监听主区域滚动，同步更新侧边栏
  useEffect(() => {
    const mainContent = mainContentRef.current;
    if (!mainContent) return;

    const handleScroll = () => {
      if (!numPages || isUserNavigating.current) return;

      const scrollTop = mainContent.scrollTop;
      const containerHeight = mainContent.clientHeight;

      // 计算当前可视区域中心位置对应的页面
      const centerPosition = scrollTop + containerHeight / 2;

      let currentPage = 1;
      let accumulatedHeight = 0;

      // 遍历所有页面元素，找到中心位置对应的页面
      for (let i = 0; i < numPages; i++) {
        const pageElement = document.getElementById(`page_${i + 1}`);
        if (pageElement) {
          const pageHeight = pageElement.offsetHeight;
          const pageTop = accumulatedHeight;
          const pageBottom = accumulatedHeight + pageHeight;

          // 如果中心位置在当前页面范围内
          if (centerPosition >= pageTop && centerPosition <= pageBottom) {
            currentPage = i + 1;
            break;
          }

          accumulatedHeight += pageHeight + 20; // 20px是页面间距
        }
      }

      setPageNumber(currentPage);
    };

    mainContent.addEventListener('scroll', handleScroll);
    return () => {
      mainContent.removeEventListener('scroll', handleScroll);
    };
  }, [numPages, showSidebar]);

  // 当侧边栏打开且页面编号改变时，滚动到对应缩略图
  useEffect(() => {
    // 避免初始加载时的滚动
    if (isInitialScroll.current) {
      isInitialScroll.current = false;
      return;
    }

    // 完全禁用在缩略图点击或用户导航时的侧边栏滚动
    if (isThumbnailClick.current || isUserNavigating.current) {
      return;
    }

    // 只有在主内容滚动导致页面变化时才同步侧边栏滚动
    if (showSidebar) {
      setTimeout(() => {
        // 三重检查，确保不是用户主动操作
        if (sidebarRef.current && !isThumbnailClick.current && !isUserNavigating.current) {
          const thumbnailElement = document.querySelector(
            `.${styled.thumbnail}[data-page="${pageNumber}"]`,
          );
          if (thumbnailElement) {
            thumbnailElement.scrollIntoView({
              behavior: 'smooth',
              block: 'center',
            });
          }
        }
      }, 100);
    }
  }, [pageNumber, showSidebar]);

  const handleClose = () => {
    dispatchInUtils({
      type: 'pageLayout/changePageMode',
      payload: '',
    });
    dispatchInUtils({
      type: 'pdfContainer/changeUrl',
      payload: {
        url: '',
        name: '',
        size: '',
      },
    });
  };

  return (
    <div className={styled.pdfViewer}>
      <div className={classNames(styled.pdfViewerToolsBar, { [styled.isRoute]: isRoute })}>
        <div className={styled.btnThumbControl} onClick={handleToggleButton}>
          <img src={thumbControlIcon} />
        </div>
        <div className={styled.title}>{name}</div>
        <div className={styled.btnToBig} onClick={handlePresentationMode}>
          <img src={toBigIcon} />
        </div>
        {!isRoute && (
          <div className={styled.btnClose} onClick={handleClose}>
            <img src={closeIcon} />
          </div>
        )}
      </div>
      <div className={styled.pdfContainer}>
        {/* 侧边栏缩略图 */}
        {showSidebar && numPages && (
          <div className={styled.sidebar} ref={sidebarRef}>
            <div className={styled.thumbnailContainer}>
              <Document file={url}>
                {Array.from(new Array(numPages), (_, index) => (
                  <div
                    key={`thumbnail_${index + 1}`}
                    className={`${styled.thumbnail} ${
                      pageNumber === index + 1 ? styled.activeThumbnail : ''
                    }`}
                    onClick={() => goToPage(index + 1)}
                    data-page={index + 1}
                  >
                    <Page pageNumber={index + 1} scale={0.09} className={styled.thumbnailPage} />
                    <div className={styled.pageNumber}>{index + 1}</div>
                  </div>
                ))}
              </Document>
            </div>
          </div>
        )}

        {/* 主要PDF显示区域 */}
        <div className={styled.mainContent} ref={mainContentRef}>
          <Document file={url} onLoadSuccess={onDocumentLoadSuccess} className={styled.pdfDocument}>
            {Array.from(new Array(numPages), (_, index) => (
              <div
                className={styled.pageContainer}
                key={`page_${index + 1}`}
                id={`page_${index + 1}`}
              >
                <Page pageNumber={index + 1} scale={scale} className={styled.pdfPage} />
              </div>
            ))}
          </Document>
        </div>
      </div>
    </div>
  );
};
export default PdfViewer;
