import {
  ALWAYS_SHOW_ONLINE_QUERY_STATUS_APPS,
  ALWAYS_SHOW_RICH_TEXT_CONTENT_APPS,
  IMAGE_SECOND_NAV_ITEMS_4_5_6,
  OFFICE_PLACEHOLDER,
} from '@/config';
import { PPTItem } from '@/models/historyChat';
import Chat from '@/pages/Chat/Chat';
import { fetchNewChatId } from '@/services/chat';
import styles from '@/styles/officetemplate-common.less';
import { dispatchInUtils, getState } from '@/utils';
import { Toast } from '@douyinfe/semi-ui';
import { FileItem } from '@douyinfe/semi-ui/lib/es/upload';
import { useEffect, useRef, useState } from 'react';
import { useDispatch, useLocation, useNavigate } from 'umi';
import { NavMenu, ToolList } from './components';
import HistoryPPT from './components/HistoryPPT';
import PolicyInterpretation from './components/policyInterpretation';
import ReadingPannel from './components/ReadingPannel';
import { useOfficeTemplate } from './hooks/useOfficeTemplate';
import { deletePPT } from './office';
import Text2Img from './Text2Img';

export default function Office() {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const location = useLocation();
  const chatRef = useRef<any>(null);
  // 二级功能code
  const [appInfo, setAppInfo] = useState<Record<string, string>>({});
  const [showMenuAndCards, setShowMenuAndCards] = useState<boolean>(true);
  const [initialContent, setInitialContent] = useState('');
  const [isNavigating, setIsNavigating] = useState(false);
  const [policyInterpretationCode, setPolicyInterpretationCode] = useState<number>(0);
  const [policyInterpretationPrompt, setPolicyInterpretationPrompt] = useState<string>('');

  const {
    activeNav,
    tools: officeTools,
    loading,
    selectedCardIndex,
    handleNavClick,
    handleTemplateSelect: handleCardClick,
    clearRichTextState,
    pptList,
    pptLoading,
    pptListTotal,
    getHistoryPPTData, // 从自定义 hook 中解构出 getHistoryPPTData 方法
  } = useOfficeTemplate();

  // const imageConfig = useSelector((state: any) => state.chat.imageConfig);

  const handleAppInfoChange = (mainKey: string, subKey: string) => {
    setAppInfo((prev) => ({
      ...prev,
      [mainKey]: subKey,
    }));
  };

  // 监听路由变化，确保每次进入页面都清除富文本状态
  useEffect(() => {
    if (location.pathname.includes('/office')) {
      clearRichTextState();
    }
  }, [location.pathname]);

  // 组件卸载时清除富文本状态
  useEffect(() => {
    return () => {
      clearRichTextState();
    };
  }, []);

  // 处理消息发送事件
  const handleSendMessage = async (content: string, attachments: FileItem[], imgConfig?: any) => {
    if (isNavigating) {
      return;
    }

    setIsNavigating(true);
    clearRichTextState();

    // const modeCode = 'write';
    const queryParams: Record<string, string> = {
      type: activeNav,
      from: activeNav,
    };

    if (selectedCardIndex !== null && officeTools[selectedCardIndex]) {
      try {
        const params = {
          appCode: activeNav,
        };
        const toolItem = officeTools[selectedCardIndex];
        if (toolItem?.id) {
          queryParams.code = toolItem.id;
        }

        const res = await fetchNewChatId(params);
        const stateData = {
          id: toolItem.id,
          content,
          attachments: JSON.stringify(attachments),
          fromOffice: true,
          imgConfig,
        };

        if (res.data) {
          dispatch({
            type: 'historyChat/unshiftChat',
            payload: {
              chatId: res.data,
              chatTitle: content.substring(0, 20) || '新对话',
              appCode: activeNav,
              source: '',
            },
          });
          let queryString = new URLSearchParams(queryParams).toString();
          dispatch({
            type: 'chat/setRealTimeContent',
            payload: '',
          });
          const path = '/chat';
          if (activeNav === 'write') queryString += '&pagemode=write';

          navigate(`${path}/${res.data}?${queryString}`, {
            state: stateData,
          });
        } else {
          setIsNavigating(false);
        }
      } catch (error) {
        setIsNavigating(false);
      }
    } else {
      try {
        const params = {
          appCode: activeNav,
        };
        // const queryString = new URLSearchParams(queryParams).toString();
        const res = await fetchNewChatId(params);
        const isOnlineState = getState().chat.isOnline;
        const stateData = {
          content,
          attachments: JSON.stringify(attachments),
          fromOffice: true,
          isOnlineState,
          imgConfig,
        };
        if (res.data) {
          // 先获取最新的历史列表
          await dispatch({
            type: 'historyChat/fetchHistoryList',
          });

          if (activeNav === 'image') {
            const subMenuKey = appInfo[activeNav];
            if (subMenuKey) queryParams.code = subMenuKey;
          }

          // 然后添加新对话
          dispatch({
            type: 'historyChat/unshiftChat',
            payload: {
              chatId: res.data,
              chatTitle: content ? content.substring(0, 20) : '新对话',
              appCode: activeNav,
              routeId: appInfo[activeNav] || '',
            },
          });

          dispatch({
            type: 'chat/setRealTimeContent',
            payload: '',
          });

          let queryString = new URLSearchParams(queryParams).toString();
          const path = '/chat';
          if (activeNav === 'write') queryString += '&pagemode=write';

          navigate(`${path}/${res.data}?${queryString}`, {
            state: stateData,
          });
          // navigate(`/chat-read/${res.data}?${queryString}`, {
          //   state: stateData,
          // });
          // navigate(`/chat/${res.data}?${queryString}`, {
          //   state: stateData,
          // });
        } else {
          setIsNavigating(false);
        }
      } catch (error) {
        console.error('获取聊天ID失败:', error);
        setIsNavigating(false);
      }
    }
  };
  // 智能PPT下载方法
  const handleDownload = async (item: PPTItem) => {
    try {
      const response = await fetch(item.pptUrl);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = item.title;
      a.click();

      URL.revokeObjectURL(url);
      // Toast.success('下载完成');
    } catch (error) {
      console.error('下载失败:', error);
    }
  };

  // 智能PPT删除方法
  const handleDelete = async (item: PPTItem) => {
    const { data } = await deletePPT(item.id);

    if (data) {
      Toast.success('删除成功');
      const user = getState().auth.user;
      await getHistoryPPTData(user.id, pageSize, pageNum); // 调用 getHistoryPPTData 方法更新数据
    }
  };

  // 智能PPT跳转方法
  const handleItemClick = (item: PPTItem) => {
    if (item.pdfUrl && item.title) {
      window.open(
        `/ppt-view?url=${item.pdfUrl}&name=${encodeURIComponent(item?.title || '')}`,
        '_blank',
      );
    } else {
      console.error('pdfUrl 或 title 为空，无法打开新窗口');
    }
  };
  const [pageNum, setPageNum] = useState(1);
  const pageSize = 9;
  // 分页页码改变
  const handlePageChange = async (pageNum: number) => {
    const user = getState().auth.user;
    setPageNum(pageNum);
    await getHistoryPPTData(user.id, pageSize, pageNum); // 调用 getHistoryPPTData 方法更新数据
  };

  useEffect(() => {
    setPageNum(1); // 重置页码为1
  }, [activeNav]);

  const handleSendMessage_reading = async (content: string, attachments: FileItem[]) => {
    if (isNavigating) {
      return;
    }

    let modeCode = 'chat';
    let ucontent = content;

    let pagemod = '';
    let docurl = '';
    let docname = '';

    let fileExt = '';
    if (attachments && attachments[0] && attachments[0].url) {
      let exttmps = attachments[0].url.split('?')[0].split('.');
      fileExt = exttmps[exttmps.length - 1];
    }

    if (fileExt && ['pdf', 'doc', 'docx', 'ppt', 'pptx'].indexOf(fileExt) > -1) {
      docurl = attachments[0].url;
      docname = attachments[0].name;

      dispatch({
        type: 'pdfContainer/changeUrl',
        payload: {
          url: docurl,
          name: docname,
          size: attachments[0].size,
        },
      });

      pagemod = 'doc';
      if (!ucontent) ucontent = '阅读' + docname;

      modeCode = 'read';
    }
    setIsNavigating(true);

    try {
      const params = {
        appCode: modeCode,
      };
      const res = await fetchNewChatId(params);
      const isOnlineState = getState().chat.isOnline;
      const stateData = {
        content: ucontent,
        attachments: JSON.stringify(attachments),
        isOnlineState,
      };
      if (res.data) {
        dispatch({
          type: 'historyChat/unshiftChat',
          payload: {
            chatId: res.data,
            chatTitle: ucontent ? ucontent.substring(0, 20) : '新对话',
            appCode: modeCode,
          },
        });

        navigate(`/chat/${res.data}?type=${modeCode}&pagemode=${pagemod}`, {
          state: stateData,
        });
      }
    } catch (error) {
      console.error('Failed to get chatId:', error);
    }
  };

  const handleSendMessage_pol_exp = async (content: string) => {
    if (isNavigating) {
      return;
    }
    let modeCode = 'pol_exp';
    let ucontent = content;

    setIsNavigating(true);

    const queryParams: Record<string, string> = {
      type: activeNav,
    };
    try {
      const params = {
        appCode: modeCode,
      };
      const res = await fetchNewChatId(params);
      const isOnlineState = getState().chat.isOnline;
      const stateData = {
        content: ucontent,
        isOnlineState,
        prompt: policyInterpretationPrompt,
      };
      if (res.data) {
        dispatch({
          type: 'historyChat/unshiftChat',
          payload: {
            chatId: res.data,
            chatTitle: ucontent ? ucontent.substring(0, 20) : '新对话',
            appCode: modeCode,
            routeId: policyInterpretationCode || '',
            prompt: policyInterpretationPrompt,
          },
        });
        navigate(`/chat/${res.data}?type=${modeCode}&code=${policyInterpretationCode}`, {
          state: stateData,
        });
      }
    } catch (error) {
      console.error('Failed to get chatId:', error);
    }
  };

  let pannelTpl = null;
  if (activeNav === 'write') {
    pannelTpl = (
      <ToolList
        loading={loading}
        tools={officeTools}
        selectedCardIndex={selectedCardIndex}
        onCardClick={handleCardClick}
      />
    );
  } else if (activeNav === 'reading') {
    pannelTpl = <ReadingPannel />;
    //pannelTpl = '';
  } else if (activeNav === 'ppt') {
    pannelTpl = (
      <HistoryPPT
        loading={pptLoading}
        tools={pptList}
        total={pptListTotal}
        onDownload={handleDownload}
        onDelete={handleDelete}
        onItemClick={handleItemClick}
        page={pageNum}
        onPageChange={handlePageChange}
        pageSize={pageSize}
      />
    );
  } else if (activeNav === 'image') {
    pannelTpl = (
      <Text2Img
        activeTab={appInfo['image'] || ''}
        onTabChange={(tab) => handleAppInfoChange('image', tab)}
      />
    );
  } else if (activeNav === 'pol_exp') {
    pannelTpl = (
      <PolicyInterpretation
        getSelectActiveTab={(e: number) => {
          setPolicyInterpretationCode(e);
        }}
        getSelectAvtivePrompt={(e: string) => {
          setPolicyInterpretationPrompt(e);
        }}
        sendMessage={(message: string) => {
          handleSendMessage_pol_exp(message);
        }}
      />
    );
  }

  useEffect(() => {
    if (ALWAYS_SHOW_ONLINE_QUERY_STATUS_APPS.includes(activeNav)) {
      dispatch({
        type: 'chat/setOnlineStatus',
        payload: true,
      });
    }
    if (ALWAYS_SHOW_RICH_TEXT_CONTENT_APPS.includes(activeNav)) {
      dispatchInUtils({
        type: 'chat/updateRichTextAreaContent',
        payload: '',
      });
    }
  }, [activeNav]);

  let chatTpl = null;
  const navListOfImageApp = getState().text2Img.navList || [];

  const currNavInfo = navListOfImageApp.find(
    (item: any) => `${item?.routeId}` === appInfo[activeNav],
  );

  const getPlaceholder = () => {
    if (activeNav === 'image' && IMAGE_SECOND_NAV_ITEMS_4_5_6.includes(appInfo[activeNav])) {
      return currNavInfo?.prompt || '';
    }
    return OFFICE_PLACEHOLDER[activeNav as keyof typeof OFFICE_PLACEHOLDER]?.placeholder || '';
  };

  if (['write', 'ppt', 'image'].includes(activeNav)) {
    chatTpl = (
      <div className={styles.chat}>
        <Chat
          key={activeNav}
          appCode={activeNav}
          childCode={appInfo['image'] || ''}
          ref={chatRef}
          customHandleSend={handleSendMessage}
          initialInputContent={initialContent}
          customChatFunction={{}}
          placeholder={getPlaceholder()}
        />
      </div>
    );
  } else if (activeNav === 'reading') {
    chatTpl = chatTpl = (
      <div className={styles.chat}>
        <Chat
          key="reading"
          appCode={activeNav}
          ref={chatRef}
          customHandleSend={handleSendMessage_reading}
          initialInputContent={initialContent}
          customChatFunction={{}}
        />
      </div>
    );
  } else if (activeNav === 'pol_exp') {
    chatTpl = chatTpl = (
      <div className={styles.chat}>
        <Chat
          ref={chatRef}
          key="pol_exp"
          appCode={activeNav}
          childCode={policyInterpretationCode}
          customHandleSend={handleSendMessage_pol_exp}
        />
      </div>
    );
  }

  return (
    <div className={styles.officePage}>
      {showMenuAndCards && (
        <>
          <div className={styles.fixedContentArea}>
            <NavMenu currentNav={activeNav} onNavClick={handleNavClick} />
            {pannelTpl}
          </div>
        </>
      )}

      {chatTpl}
    </div>
  );
}
