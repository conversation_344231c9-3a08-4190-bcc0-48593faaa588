import EmptyImage from '@/assets/chat/fallbackImage.svg';
import VideoPlay from '@/assets/office/videoPlay.svg';
import deleteImage from '@/assets/svg/deletesm.svg';
import LazyImage from '@/components/LazyImage';
import useInfiniteScroll from '@/hooks/useInfiniteScroll';
import { VisualEngineeringListItemType } from '@/services/visualEngineering';
import { formatTimestamp } from '@/utils/util';
import { Popconfirm, Spin, Typography } from '@douyinfe/semi-ui';
import {
  MediaControlBar,
  MediaController,
  MediaFullscreenButton,
  MediaPlaybackRateButton,
  MediaPlayButton,
  MediaTimeRange,
} from 'media-chrome/react';
import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import ReactPlayer from 'react-player';
import ImagePreviewer from '../ImagePreviewer';
import styles from './index.less';

export interface GalleryProps {
  active: boolean;
  uploadConfig: {
    uploadTip: string;
    uploadType: string;
    uploadSizeLimit: number;
    uploadLimit: number;
  };
  pageSize?: number;
  refreshKey: number;
  fetchImages: () => Promise<VisualEngineeringListItemType[]>;
  onDelete: (id: number) => Promise<boolean>;
  stopVideoPlay: () => void;
}

const RecordList = forwardRef<{ loadMoreImages: () => void }, GalleryProps>(
  (
    { active, uploadConfig, refreshKey, pageSize = 10, fetchImages, onDelete, stopVideoPlay },
    ref,
  ) => {
    const { Text } = Typography;
    const previewRefs = useRef<Record<number, { open: () => void }>>({});
    const [allImages, setAllImages] = useState<VisualEngineeringListItemType[]>([]);
    const allImagesRef = useRef(allImages);
    const [images, setImages] = useState<VisualEngineeringListItemType[] | null>(null);
    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [playingVideoId, setPlayingVideoId] = useState<number | null>(null);

    useEffect(() => {
      const initData = async () => {
        try {
          setLoading(true);
          const data = await fetchImages();
          setAllImages(data);
          setImages(data.slice(0, pageSize));
          setHasMore(data.length > pageSize);
          setError(null);
        } catch (error) {
          setError('加载图片失败');
          console.error('Failed to fetch images:', error);
        } finally {
          setLoading(false);
        }
      };
      if (active) {
        initData();
        setPage(1);
      }
    }, [active, refreshKey]);

    const loadMoreImages = useCallback(async () => {
      if (!hasMore || loading) return;

      setLoading(true);
      try {
        const startIndex = page * pageSize;
        const endIndex = startIndex + pageSize;
        const nextImages = allImagesRef.current.slice(startIndex, endIndex);

        await new Promise((resolve) => setTimeout(resolve, 200));

        setImages((prev) => [...(prev || []), ...nextImages]);
        setPage((prev) => prev + 1);
        setHasMore(endIndex < allImagesRef.current.length);
        setError(null);
      } catch (err) {
        setError('加载更多失败');
        console.error('Failed to load more images:', err);
      } finally {
        setLoading(false);
      }
    }, [hasMore, loading, page, pageSize]);

    // 使用 Intersection Observer 替代滚动事件
    const { triggerRef } = useInfiniteScroll(loadMoreImages, hasMore, loading, {
      rootMargin: '100px 0px',
      threshold: 0.1,
      debounceMs: 150,
    });

    useEffect(() => {
      allImagesRef.current = allImages;
    }, [allImages]);

    const handleImageClick = useCallback((id: number) => {
      previewRefs.current[id]?.open();
    }, []);

    const pauseAllVideos = () => {
      // 只获取当前组件内的视频元素并暂停
      const videos = document.querySelectorAll(`.${styles.recordList} video`);
      videos.forEach((video: any) => {
        if (!video.paused) {
          video.pause();
        }
      });
      // 重置播放状态
      setPlayingVideoId(null);
    };

    useImperativeHandle(
      ref,
      () => ({
        loadMoreImages,
        pauseAllVideos,
      }),
      [loadMoreImages, pauseAllVideos],
    );

    // 优化删除操作
    const handleDelete = useCallback(
      async (id: number) => {
        try {
          if (await onDelete(id)) {
            setAllImages((prev) => prev.filter((img) => img.id !== id));
            setImages((prev) => (prev ? prev.filter((img) => img.id !== id) : null));
            return true;
          }
          return false;
        } catch (error) {
          console.error('删除失败:', error);
          return false;
        }
      },
      [onDelete],
    );

    const renderGalleryItemFooter = useCallback(
      (item: VisualEngineeringListItemType) => {
        return (
          <div className={styles.recordListContentItemBot}>
            <div className={styles.recordListContentItemBotTL}>
              {item.identCount != null && item?.identCount >= 0 ? (
                <div>
                  <div className={styles.recordListContentItemBotTLT}>模型结果</div>
                  <div
                    className={`${styles.recordListContentItemBotTLT} ${styles.noWeight} ${styles.resultContent}`}
                  >
                    数量：{item.identCount}
                  </div>
                </div>
              ) : item.resultList?.length ? (
                <div>
                  <div className={styles.recordListContentItemBotTLT}>模型结果</div>
                  <Text
                    style={{ width: 176 }}
                    ellipsis={{
                      rows: 1,
                      showTooltip: {
                        opts: {
                          style: {
                            width: 166,
                            maxWidth: 166,
                          },
                          content: (
                            <div className={styles.textTips}>
                              {item.resultList.map((resultItem, index) => (
                                <pre
                                  key={index}
                                  style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}
                                >
                                  {resultItem}
                                </pre>
                              ))}
                            </div>
                          ),
                        },
                      },
                    }}
                    className={`${styles.recordListContentItemBotTLT} ${styles.noWeight} ${styles.resultContent}`}
                  >
                    {item.resultList.join(', ')}
                  </Text>
                </div>
              ) : (
                <div className={styles.recordListContentItemBotTLT}>
                  模型结果
                  <p
                    className={`${styles.recordListContentItemBotTLT} ${styles.noWeight} ${styles.resultContent}`}
                  >
                    --
                  </p>
                </div>
              )}
              <div className="flex items-center">
                <div className={styles.recordListContentItemBotTLB}>
                  {formatTimestamp(item.createTime)}
                </div>
                <Popconfirm
                  title="确定要删除此条记录吗？"
                  content="删除后，内容无法恢复。"
                  onConfirm={() => handleDelete(item.id)}
                >
                  <div
                    className={styles.recordListContentItemBotTR}
                    onClick={(e) => {
                      e.stopPropagation();
                    }}
                  >
                    <img src={deleteImage} alt="" className={styles.recordListContentItemDelete} />
                  </div>
                </Popconfirm>
              </div>
            </div>
          </div>
        );
      },
      [handleDelete],
    );

    return (
      <div className={styles.recordList}>
        <div className={styles.recordListName}>最近记录</div>
        {images == null ? null : images.length ? (
          <div className={styles.recordListContent}>
            {images.map((item) => (
              <div
                className={styles.galleryItem}
                key={item.id}
                onClick={() =>
                  !uploadConfig?.uploadTip?.includes('视频') && handleImageClick(item.id)
                }
              >
                {uploadConfig?.uploadTip?.includes('视频') ? (
                  <div className={styles.videoCont}>
                    {playingVideoId === item.id ? null : (
                      <div className={styles.videoContW}>
                        <img
                          src={VideoPlay}
                          alt="play"
                          className={styles.videoContWI}
                          onClick={(e) => {
                            e.stopPropagation();
                            setPlayingVideoId(item.id);
                          }}
                        />
                      </div>
                    )}
                    <MediaController
                      style={{
                        width: '100%',
                        height: '100%',
                      }}
                    >
                      <ReactPlayer
                        slot="media"
                        style={{
                          width: '100%',
                          height: '100%',
                        }}
                        controls={false}
                        src={item.identUrl}
                        preload="metadata"
                        playing={playingVideoId === item.id}
                        onPlay={() => {
                          if (playingVideoId !== null && playingVideoId !== item.id) {
                            const prevVideo = document.getElementById(
                              `video-${playingVideoId}`,
                            ) as HTMLVideoElement;
                            if (prevVideo) {
                              prevVideo.pause();
                            }
                          }
                          stopVideoPlay();
                          // 设置当前视频为正在播放状态
                          setPlayingVideoId(item.id);
                        }}
                        // onPause={() => {
                        //   // 当视频暂停时更新状态
                        //   if (playingVideoId === item.id) {
                        //     setPlayingVideoId(null);
                        //   }
                        // }}
                        onEnded={() => {
                          // 视频播放结束时重置状态
                          if (playingVideoId === item.id) {
                            setPlayingVideoId(null);
                          }
                        }}
                        id={`video-${item.id}`}
                      />
                      <MediaControlBar>
                        <MediaPlayButton />
                        {/* <MediaSeekBackwardButton seekOffset={10} />
                        <MediaSeekForwardButton seekOffset={10} /> */}
                        <MediaTimeRange />
                        {/* <MediaTimeDisplay showDuration /> */}
                        {/* <MediaMuteButton /> */}
                        {/* <MediaVolumeRange /> */}
                        <MediaPlaybackRateButton />
                        <MediaFullscreenButton />
                      </MediaControlBar>
                    </MediaController>
                  </div>
                ) : (
                  <LazyImage
                    url={item.identUrl}
                    alt={''}
                    height={221}
                    className={styles.lazyImage}
                  />
                )}
                {renderGalleryItemFooter(item)}
                <ImagePreviewer
                  ref={(el) => {
                    if (el) previewRefs.current[item.id] = el;
                    else delete previewRefs.current[item.id];
                  }}
                  mainUrl={item.identUrl}
                  allUrls={[item.identUrl]}
                />
              </div>
            ))}
          </div>
        ) : (
          <div className={styles.emptyMessageContainer}>
            <img className={styles.emptyImage} src={EmptyImage} />
            <div className={styles.emptyMessageText}>暂无历史记录</div>
          </div>
        )}
        {/* Intersection Observer 触发器 */}
        {hasMore && images && images.length > 0 && (
          <div ref={triggerRef} className={styles.loadTrigger} />
        )}

        {loading && (
          <div className={styles.recordListSpin}>
            <Spin size="large" />
            <span>加载中...</span>
          </div>
        )}

        {error && (
          <div className={styles.errorMessage}>
            <p>{error}</p>
          </div>
        )}

        {!hasMore && images && images.length > 0 && (
          <div className={styles.endMessage}>
            <p>已经到底啦~</p>
          </div>
        )}
      </div>
    );
  },
);

export default RecordList;
