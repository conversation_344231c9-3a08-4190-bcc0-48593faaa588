.pdfViewer {
  flex: 1;
  box-sizing: border-box;
  padding-right: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .pdfViewerToolsBar {
    height: 53px;
    border-bottom: 1px solid #eee;
    display: flex;
    flex-direction: row;
    align-items: center;
    box-sizing: border-box;
    padding: 0 10px;
    position: relative;
    z-index: 100;

    &.isRoute {
      padding: 20px;
    }

    .btnThumbControl {
      padding-right: 10px;
      box-sizing: border-box;
      cursor: pointer;

      img {
        width: 24px;
        height: auto;
      }
    }

    .title {
      flex: 1;
    }

    .btnToBig {
      padding-left: 10px;
      box-sizing: border-box;
      cursor: pointer;

      img {
        width: 24px;
        height: auto;
      }
    }

    .btnClose {
      padding-left: 10px;
      box-sizing: border-box;
      cursor: pointer;

      img {
        width: 24px;
        height: auto;
      }
    }
  }

  .pdfContainer {
    flex: 1;
    height: 100%;
    display: flex;
    background-color: #f5f5f5;
  }

  .sidebar {
    width: 215px;
    background-color: #fff;
    border-right: 1px solid #ddd;
    overflow-y: auto;
    padding: 10px;
    box-sizing: border-box;
  }

  .thumbnailContainer {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .thumbnail {
    cursor: pointer;
    border: 2px solid transparent;
    border-radius: 4px;
    padding: 5px;
    text-align: center;
    transition: border-color 0.2s;

    &:hover {
      border-color: #ccc;
    }

    &.activeThumbnail {
      border-color: #1890ff;
      background-color: #f0f8ff;
    }
  }

  .thumbnailPage {
    width: 100%;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .pageNumber {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
  }

  .mainContent {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
  }

  .pdfDocument {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
  }

  .pageContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
  }

  .pdfPage {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    overflow: hidden;
  }
}
