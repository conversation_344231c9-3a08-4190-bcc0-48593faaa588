import Arrow from '@/assets/svg/arrow.svg';
import Empty from '@/assets/svg/emi.svg';
import UploadImageSvg from '@/assets/svg/upload.svg';
import { uploadApi } from '@/services/chat';
import { visualEngineering, VisualEngineeringListItemType } from '@/services/visualEngineering';
import { getAccessToken, getTenantId } from '@/utils';
import { Carousel, Image, ImagePreview, Spin, Toast, Upload } from '@douyinfe/semi-ui';
import { FileItem } from '@douyinfe/semi-ui/lib/es/upload';
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import ReactPlayer from 'react-player';
import styles from './index.less';

// 常量配置
const UPLOAD_CONFIG = {
  url: process.env.API_URL + uploadApi,
  data: {
    path: 'ai/portal/',
    configName: 'aifile',
  },
  // maxSize: 6 * 1024 * 1024, // 6M
  // maxCount: 5,
  // acceptTypes: '.jpg,.jpeg,.png,.tiff,.bmp',
} as const;

const getUploadHeaders = () => ({
  'tenant-id': getTenantId(),
  authorization: 'Bearer ' + getAccessToken(),
});

// 组件状态类型定义
interface UploadState {
  fileList: FileItem[];
  originImages: string[];
  processedImages: VisualEngineeringListItemType[];
  isProcessing: boolean;
  error: string | null;
}

// 预览状态类型
interface PreviewState {
  visible: boolean;
  currentIndex: number;
}

interface Props {
  appCode: string;
  routeId: number;
  deleteId: number;
  upRecordList: () => void;
  stopVideoPlay: () => void;
  uploadConfig: {
    uploadTip: string;
    uploadType: string;
    uploadSizeLimit: number;
    uploadLimit: number;
  };
}

const UploadImageOptimized = forwardRef<{}, Props>(
  ({ appCode, routeId, deleteId, uploadConfig, upRecordList, stopVideoPlay }, ref) => {
    // Refs
    const uploadRef = useRef<Upload>(null);
    const originCarouselRef = useRef<Carousel>(null);
    const processedCarouselRef = useRef<Carousel>(null);
    const [playingVideoId, setPlayingVideoId] = useState<number | null>(null);

    const [uploadState, setUploadState] = useState<UploadState>({
      fileList: [],
      originImages: [],
      processedImages: [],
      isProcessing: false,
      error: null,
    });

    const [originPreview, setOriginPreview] = useState<PreviewState>({
      visible: false,
      currentIndex: 0,
    });

    const [processedPreview, setProcessedPreview] = useState<PreviewState>({
      visible: false,
      currentIndex: 0,
    });

    const pauseAllVideos = () => {
      // 只获取当前组件内的视频元素并暂停
      const videos = document.querySelectorAll(`.${styles.swiperCarosel} video`);
      const videoHls = document.querySelectorAll(`.${styles.swiperCarosel} hls-video`);
      videos.forEach((video: any) => {
        if (!video.paused) {
          video.pause();
        }
      });
      videoHls.forEach((video: any) => {
        if (!video.paused) {
          video.pause();
        }
      });
      // 重置播放状态
      setPlayingVideoId(null);
    };

    // 重置上传区域
    const resetUploadArea = () => {
      setUploadState({
        fileList: [],
        originImages: [],
        processedImages: [],
        isProcessing: false,
        error: null,
      });
      uploadRef.current?.clear();
    };

    const isVideoUpload = (uploadTip: string): boolean => {
      return uploadTip?.includes('视频');
    };

    const uploadTypeText = isVideoUpload(uploadConfig.uploadTip) ? '视频' : '图片';
    const uploadTypeUnit = isVideoUpload(uploadConfig.uploadTip) ? '个' : '张';

    const handleFileChange = useCallback(({ fileList }: { fileList: FileItem[] }) => {
      setUploadState((prev) => ({
        ...prev,
        fileList,
        error: null,
      }));

      const allUploaded = fileList.every((file) => file.status === 'success');
      if (allUploaded && fileList.length > 0) {
        stopVideoPlay();
        pauseAllVideos();
        processImages(fileList);
      }
    }, []);

    // 图像处理函数
    const processImages = useCallback(
      async (fileList: FileItem[]) => {
        try {
          setUploadState((prev) => ({ ...prev, isProcessing: true, error: null }));

          const uploadUrls = fileList
            .filter((file) => file.response?.data)
            .map((file) => file.response.data);

          if (uploadUrls.length === 0) {
            throw new Error(`没有有效的上传${uploadTypeText}`);
          }

          const res = await visualEngineering({ appCode, routeId, uploadUrls });

          if (res.data) {
            setUploadState((prev) => ({
              ...prev,
              originImages: uploadUrls,
              processedImages: res.data,
              fileList: [], // 清空文件列表
            }));
          } else {
            resetUploadArea();
          }
        } catch (error) {
          resetUploadArea();
        } finally {
          setUploadState((prev) => ({ ...prev, isProcessing: false }));
        }
      },
      [routeId],
    );

    // 清除所有数据的函数
    const clearAllData = useCallback(() => {
      setUploadState({
        fileList: [],
        originImages: [],
        processedImages: [],
        isProcessing: false,
        error: null,
      });
      setOriginPreview({ visible: false, currentIndex: 0 });
      setProcessedPreview({ visible: false, currentIndex: 0 });
    }, []);

    // 预览控制函数
    const showOriginPreview = useCallback((index: number) => {
      setOriginPreview({ visible: true, currentIndex: index });
    }, []);

    const showProcessedPreview = useCallback((index: number) => {
      setProcessedPreview({ visible: true, currentIndex: index });
    }, []);

    // 轮播控制函数
    const handleCarouselNavigation = useCallback(
      (direction: 'prev' | 'next') => {
        if (uploadState.processedImages.length <= 1) return;

        isVideoUpload(uploadConfig.uploadTip) && pauseAllVideos();
        const action = direction === 'prev' ? 'prev' : 'next';
        originCarouselRef.current?.[action]();
        processedCarouselRef.current?.[action]();
      },
      [uploadState.processedImages.length],
    );

    // 文件校验函数
    const validateFile = useCallback((file: File): { valid: boolean; error?: string } => {
      // 检查文件大小
      if (file.size > uploadConfig.uploadSizeLimit * 1024 * 1024) {
        return {
          valid: false,
          error: `${uploadTypeText}"${file.name}"大小超出限制（最大${uploadConfig.uploadSizeLimit}MB）`,
        };
      }

      // 检查文件类型
      const allowedTypes = uploadConfig.uploadType.split(',').map((type) => type.trim());
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
      if (!allowedTypes.includes(fileExtension)) {
        return {
          valid: false,
          error: `${uploadTypeText}"${file.name}"格式不支持，仅支持：${uploadConfig.uploadType}`,
        };
      }

      // 检查文件名
      if (file.name.length > 100) {
        return {
          valid: false,
          error: `${uploadTypeText}"${file.name}"名称过长（最大100字符）`,
        };
      }

      return { valid: true };
    }, []);

    // 批量校验所有文件
    const validateAllFiles = useCallback(
      (files: File[]): { valid: boolean; errors: string[] } => {
        const errors: string[] = [];

        // 检查文件数量
        if (files.length > uploadConfig.uploadLimit) {
          errors.push(
            `${uploadTypeText}数量超出限制，最多只能上传${uploadConfig.uploadLimit}${uploadTypeUnit}${uploadTypeText}`,
          );
          return { valid: false, errors };
        }

        // 检查每个文件
        for (const file of files) {
          const validation = validateFile(file);
          if (!validation.valid && validation.error) {
            errors.push(validation.error);
          }
        }

        // 检查是否有重复文件名
        const fileNames = files.map((file) => file.name);
        const duplicateNames = fileNames.filter((name, index) => fileNames.indexOf(name) !== index);
        if (duplicateNames.length > 0) {
          errors.push(`存在重复${uploadTypeText}名：${[...new Set(duplicateNames)].join(', ')}`);
        }

        return {
          valid: errors.length === 0,
          errors,
        };
      },
      [validateFile],
    );

    // 上传前校验
    const beforeUpload = useCallback(
      ({ file, fileList }: any): boolean => {
        setUploadState((prev) => ({ ...prev, isProcessing: true, error: null }));
        // 将 FileItem 转换为 File 对象进行校验
        const files = fileList
          .map((item: any) => item.fileInstance || item)
          .filter((f: any) => f instanceof File);

        const validation = validateAllFiles(files);

        if (!validation.valid) {
          // 显示所有错误信息
          validation.errors.forEach((error) => {
            Toast.error(error);
          });

          // 更新错误状态
          setUploadState((prev) => ({
            ...prev,
            isProcessing: false,
            error: validation.errors.join('; '),
          }));

          // 清空重置上传状态
          setUploadState((prev) => ({
            ...prev,
            fileList: [], // 清空文件列表
          }));
          uploadRef.current?.clear();

          return false; // 阻止上传，但不清空文件列表，保持上传区域可见
        }

        // 清除之前的错误状态
        setUploadState((prev) => ({
          ...prev,
          error: null,
        }));

        return true; // 允许上传
      },
      [validateAllFiles],
    );

    // 错误处理函数
    const handleUploadError = useCallback((file: any) => {
      Toast.error(`上传${uploadTypeText}"${file.name}"失败，请重试`);
      setUploadState((prev) => ({
        ...prev,
        isProcessing: false,
        error: `上传${uploadTypeText}"${file.name}"失败`,
      }));
    }, []);

    const handleSizeError = useCallback((file: any) => {
      return Toast.error(`${file.name} 超出${uploadConfig.uploadSizeLimit}MB大小限制请重新上传`);
    }, []);

    const handleExceed = useCallback(() => {
      Toast.info(`最多只能上传${uploadConfig.uploadLimit}${uploadTypeUnit}${uploadTypeText}`);
    }, []);

    // 监听处理结果变化，通知父组件刷新
    useEffect(() => {
      if (uploadState.processedImages.length > 0) {
        upRecordList();
      }
    }, [uploadState.processedImages, upRecordList]);

    // 计算属性
    const uploadHeaders = useMemo(() => getUploadHeaders(), []);
    // const hasOriginImages = uploadState.originImages.length > 0;
    const hasProcessedImages = uploadState.processedImages.length > 0;
    const canNavigate = uploadState.processedImages.length > 1;

    // 判断是否应该显示上传区域（即使有错误也要显示）
    // const shouldShowUploadArea = !hasOriginImages;

    // 渲染上传区域
    const renderUploadArea = () => {
      // if (uploadState.isProcessing) {
      //   return (
      //     <div className={`${styles.uploadContainerWrapper} ${styles.processingState}`}>
      //       <div className={styles.spinWrapper}>
      //         <Spin size="large" />
      //       </div>
      //     </div>
      //   );
      // }

      // if (shouldShowUploadArea) {
      return (
        <div className={styles.uploadContainerWrapper}>
          <Upload
            ref={uploadRef}
            className={styles.uploadContainer}
            fileList={uploadState.fileList}
            fileName="file"
            action={UPLOAD_CONFIG.url}
            headers={uploadHeaders}
            data={UPLOAD_CONFIG.data}
            listType="picture"
            showPicInfo
            showUploadList={false}
            limit={uploadConfig.uploadLimit}
            maxSize={uploadConfig.uploadSizeLimit * 1024 * 1024}
            accept={uploadConfig.uploadType}
            draggable
            multiple
            beforeUpload={beforeUpload}
            onChange={handleFileChange}
            onSizeError={handleSizeError}
            onExceed={handleExceed}
            onError={handleUploadError}
          />
          <div className={styles.uploadContainerB}>
            <div className={styles.uploadWrapper}>
              <img src={UploadImageSvg} className={styles.uploadImages} alt="" />
              <div className={styles.uploadTextt}>{uploadConfig.uploadTip}</div>
            </div>
          </div>
        </div>
      );
      // }

      // return (
      //   <div className={styles.uploadContainerWrapper}>
      //     <Carousel
      //       ref={originCarouselRef}
      //       className={styles.uploadContainer}
      //       autoPlay={false}
      //       showArrow={false}
      //       showIndicator={false}
      //     >
      //       {uploadState.originImages.map((imageUrl, index) => (
      //         <img
      //           src={imageUrl}
      //           key={index}
      //           className={styles.originImage}
      //           alt={`原图 ${index + 1}`}
      //           onClick={() => showOriginPreview(index)}
      //         />
      //       ))}
      //     </Carousel>
      //     <Button
      //       className={styles.deleteButton}
      //       icon={<img src={DeleteIcon} alt="删除" />}
      //       onClick={clearAllData}
      //       title="清除所有图片"
      //     />
      //   </div>
      // );
    };

    // 渲染结果展示区域
    const renderResultArea = () => {
      if (uploadState.isProcessing) {
        return (
          <div className={styles.spinWrapper}>
            <Spin size="large" />
          </div>
        );
      }

      if (!hasProcessedImages) {
        return (
          <div className={styles.imageWrapper}>
            <img src={Empty} className={styles.uploadImage} alt="空状态" />
            <div className={styles.imageText}>{uploadTypeText}展示区</div>
          </div>
        );
      }

      return (
        <>
          <Carousel
            ref={processedCarouselRef}
            className={styles.swiperCarosel}
            autoPlay={false}
            showArrow={false}
            showIndicator={false}
          >
            {uploadState.processedImages.map((item, index) =>
              isVideoUpload(uploadConfig.uploadTip) ? (
                <div
                  key={item.id}
                  style={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <ReactPlayer
                    style={{ width: '100%', height: '100%' }}
                    key={item.id}
                    className={styles.videoPlay}
                    controls
                    src={item.identUrl}
                    preload="metadata"
                    onPlay={() => {
                      if (playingVideoId !== null && playingVideoId !== item.id) {
                        const prevVideo = document.getElementById(
                          `videou-${playingVideoId}`,
                        ) as HTMLVideoElement;
                        if (prevVideo) {
                          prevVideo.pause();
                        }
                      }
                      stopVideoPlay();
                      // 设置当前视频为正在播放状态
                      setPlayingVideoId(item.id);
                    }}
                    // onPause={() => {
                    //   // 当视频暂停时更新状态
                    //   if (playingVideoId === item.id) {
                    //     setPlayingVideoId(null);
                    //   }
                    // }}
                    onEnded={() => {
                      // 视频播放结束时重置状态
                      if (playingVideoId === item.id) {
                        setPlayingVideoId(null);
                      }
                    }}
                    id={`videou-${item.id}`}
                  />
                </div>
              ) : (
                <img
                  src={item.identUrl}
                  key={item.id}
                  className={styles.processedImage}
                  alt={`处理结果 ${index + 1}`}
                  onClick={() => showProcessedPreview(index)}
                />
              ),
            )}
          </Carousel>

          <Image
            preview={false}
            src={Arrow}
            className={styles.imageContainerL}
            style={{ cursor: canNavigate ? 'pointer' : 'not-allowed' }}
            onClick={() => handleCarouselNavigation('prev')}
          />
          <Image
            preview={false}
            src={Arrow}
            className={styles.imageContainerR}
            style={{ cursor: canNavigate ? 'pointer' : 'not-allowed' }}
            onClick={() => handleCarouselNavigation('next')}
          />
        </>
      );
    };

    useEffect(() => {
      const deleteItem = uploadState.processedImages.find((item) => item.id === deleteId);
      if (deleteItem) {
        setUploadState((prev) => ({
          ...prev,
          processedImages: prev.processedImages.filter((item) => item.id !== deleteId),
        }));
        processedCarouselRef.current?.goTo(0);
      }
    }, [deleteId]);

    useImperativeHandle(
      ref,
      () => ({
        pauseAllVideos,
        resetUploadArea,
      }),
      [pauseAllVideos],
    );

    return (
      <div className={styles.container}>
        {renderUploadArea()}

        <div className={styles.imageContainer}>{renderResultArea()}</div>

        <ImagePreview
          src={uploadState.processedImages.map((item) => item.identUrl)}
          visible={processedPreview.visible}
          currentIndex={processedPreview.currentIndex}
          onVisibleChange={() => setProcessedPreview((prev) => ({ ...prev, visible: false }))}
          onChange={(index) => setProcessedPreview((prev) => ({ ...prev, currentIndex: index }))}
        />

        <ImagePreview
          src={uploadState.originImages}
          visible={originPreview.visible}
          currentIndex={originPreview.currentIndex}
          onVisibleChange={() => setOriginPreview((prev) => ({ ...prev, visible: false }))}
          onChange={(index) => setOriginPreview((prev) => ({ ...prev, currentIndex: index }))}
        />
      </div>
    );
  },
);

export default UploadImageOptimized;
